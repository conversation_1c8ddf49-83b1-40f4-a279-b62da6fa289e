from pynput import mouse, keyboard
import time

last_activity_time = time.time()
idle_threshold = 10  # عدد الثواني قبل اعتبار الجهاز "غير نشط"

def on_mouse_move(x, y):
    global last_activity_time
    last_activity_time = time.time()

def on_click(x, y, button, pressed):
    global last_activity_time
    last_activity_time = time.time()

def on_scroll(x, y, dx, dy):
    global last_activity_time
    last_activity_time = time.time()

def on_key_press(key):
    global last_activity_time
    last_activity_time = time.time()

# تشغيل مراقبة الماوس والكيبورد
mouse_listener = mouse.Listener(
    on_move=on_mouse_move,
    on_click=on_click,
    on_scroll=on_scroll)
keyboard_listener = keyboard.Listener(on_press=on_key_press)

mouse_listener.start()
keyboard_listener.start()

# حلقة تفقد النشاط
try:
    while True:
        idle_time = time.time() - last_activity_time
        if idle_time > idle_threshold:
            print(f"🛑 المستخدم غير نشط منذ {int(idle_time)} ثانية")
        else:
            print(f"✅ المستخدم نشط، الخمول = {int(idle_time)} ثانية")
        time.sleep(2)
except KeyboardInterrupt:
    print("تم إيقاف المراقبة.")
