from pynput import mouse, keyboard
import time
from datetime import datetime
import os

# متغيرات تتبع النشاط
last_activity_time = time.time()
session_start_time = time.time()
idle_threshold = 10  # عدد الثواني قبل اعتبار الجهاز "غير نشط"
total_active_time = 0
last_status = "active"  # لتتبع آخر حالة (active/idle)
activity_log = []  # لحفظ سجل النشاطات

def save_work_data():
    """حفظ بيانات العمل في ملف .txt"""
    try:
        session_end_time = time.time()
        total_session_time = session_end_time - session_start_time

        # إنشاء اسم الملف بالتاريخ والوقت
        timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
        filename = f"work_hours_{timestamp}.txt"

        with open(filename, 'w', encoding='utf-8') as file:
            file.write("=" * 50 + "\n")
            file.write("تقرير ساعات العمل\n")
            file.write("=" * 50 + "\n\n")

            # معلومات الجلسة
            file.write(f"تاريخ الجلسة: {datetime.fromtimestamp(session_start_time).strftime('%Y-%m-%d')}\n")
            file.write(f"وقت البداية: {datetime.fromtimestamp(session_start_time).strftime('%H:%M:%S')}\n")
            file.write(f"وقت النهاية: {datetime.fromtimestamp(session_end_time).strftime('%H:%M:%S')}\n")
            file.write(f"إجمالي وقت الجلسة: {total_session_time/3600:.2f} ساعة\n")
            file.write(f"إجمالي وقت النشاط: {total_active_time/3600:.2f} ساعة\n")
            file.write(f"نسبة النشاط: {(total_active_time/total_session_time)*100:.1f}%\n\n")

            # سجل النشاطات
            file.write("سجل النشاطات:\n")
            file.write("-" * 30 + "\n")
            for log_entry in activity_log:
                file.write(f"{log_entry}\n")

        print(f"✅ تم حفظ البيانات في الملف: {filename}")
        return filename
    except Exception as e:
        print(f"❌ خطأ في حفظ البيانات: {e}")
        return None

def log_activity(status, idle_time=0):
    """تسجيل النشاط في السجل"""
    timestamp = datetime.now().strftime("%H:%M:%S")
    if status == "active":
        activity_log.append(f"{timestamp} - المستخدم نشط")
    else:
        activity_log.append(f"{timestamp} - المستخدم غير نشط منذ {int(idle_time)} ثانية")

def on_mouse_move(x, y):
    global last_activity_time, last_status, total_active_time
    current_time = time.time()

    # إذا كان المستخدم كان غير نشط، احسب الوقت النشط
    if last_status == "idle":
        last_status = "active"
        log_activity("active")

    last_activity_time = current_time

def on_click(x, y, button, pressed):
    global last_activity_time, last_status
    current_time = time.time()

    if last_status == "idle":
        last_status = "active"
        log_activity("active")

    last_activity_time = current_time

def on_scroll(x, y, dx, dy):
    global last_activity_time, last_status
    current_time = time.time()

    if last_status == "idle":
        last_status = "active"
        log_activity("active")

    last_activity_time = current_time

def on_key_press(key):
    global last_activity_time, last_status
    current_time = time.time()

    if last_status == "idle":
        last_status = "active"
        log_activity("active")

    last_activity_time = current_time

# تشغيل مراقبة الماوس والكيبورد
mouse_listener = mouse.Listener(
    on_move=on_mouse_move,
    on_click=on_click,
    on_scroll=on_scroll)
keyboard_listener = keyboard.Listener(on_press=on_key_press)

mouse_listener.start()
keyboard_listener.start()

# حلقة تفقد النشاط
print("🚀 بدء مراقبة النشاط...")
print("اضغط Ctrl+C لإيقاف المراقبة وحفظ البيانات")

try:
    while True:
        current_time = time.time()
        idle_time = current_time - last_activity_time

        if idle_time > idle_threshold:
            if last_status == "active":
                # المستخدم أصبح غير نشط
                last_status = "idle"
                log_activity("idle", idle_time)
            print(f"🛑 المستخدم غير نشط منذ {int(idle_time)} ثانية")
        else:
            if last_status == "idle":
                # المستخدم أصبح نشط
                last_status = "active"
                log_activity("active")

            # إضافة الوقت النشط
            total_active_time += 2  # نضيف ثانيتين (مدة النوم)
            print(f"✅ المستخدم نشط، الخمول = {int(idle_time)} ثانية")

        time.sleep(2)

except KeyboardInterrupt:
    print("\n⏹️ تم إيقاف المراقبة...")
    print("💾 جاري حفظ البيانات...")

    # حفظ البيانات النهائية
    filename = save_work_data()
    if filename:
        print(f"✅ تم حفظ تقرير العمل في: {filename}")

    print("👋 شكراً لاستخدام نظام مراقبة ساعات العمل!")
